#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试JSON格式的装配层次结构描述生成
"""

import os
import sys
import json

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.assembly_hierarchy_encoder import AssemblyHierarchyEncoder

def test_json_description():
    """测试JSON描述生成"""
    
    # 创建编码器实例
    encoder = AssemblyHierarchyEncoder(device="cpu")
    
    try:
        # 获取一个装配体的层次结构数据
        assembly_ids = encoder.get_all_assembly_ids()
        if not assembly_ids:
            print("未找到任何装配体")
            return
        
        # 测试前几个装配体
        test_count = min(3, len(assembly_ids))
        print(f"测试前 {test_count} 个装配体的JSON描述生成...")
        
        for i, assembly_id in enumerate(assembly_ids[:test_count], 1):
            print(f"\n=== 装配体 {i}: {assembly_id} ===")
            
            # 获取层次结构数据
            hierarchy_data = encoder.get_assembly_hierarchy_data(assembly_id)
            if not hierarchy_data:
                print(f"装配体 {assembly_id} 层次结构数据为空")
                continue
            
            # 生成JSON描述
            json_description = encoder.generate_hierarchy_description(hierarchy_data)
            print("JSON描述:")
            
            # 格式化输出JSON
            try:
                parsed_json = json.loads(json_description)
                formatted_json = json.dumps(parsed_json, indent=2, ensure_ascii=False)
                print(formatted_json)
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                print(f"原始描述: {json_description}")
            
            print("-" * 50)
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭连接
        encoder.close()

if __name__ == "__main__":
    test_json_description()
