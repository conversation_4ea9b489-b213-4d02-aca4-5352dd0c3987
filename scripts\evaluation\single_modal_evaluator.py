#!/usr/bin/env python3
"""
单模态查询评估器
用于评估单个智能体在单模态查询基准测试上的准确率
"""

import os
import sys
import json
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from src.agents.attribute_filtering_agent import AttributeFilteringAgent
from src.agents.structural_topology_agent import StructuralTopologyAgent
from src.agents.geometry_semantic_agent import GeometrySemanticAgent
from src.agents.data_models import (
    UnifiedStructuredDataTask,
    UnifiedStructuralQueryTask,
    UnifiedGeometrySemanticTask
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class SingleModalEvaluationResult:
    """单个查询的评估结果"""
    query_id: str
    query_type: str
    natural_language_prompt: str
    ground_truth_uuids: List[str]
    predicted_uuids: List[str]
    is_correct: bool
    execution_time: float
    agent_name: str
    error_message: Optional[str] = None


@dataclass
class SingleModalEvaluationSummary:
    """评估总结"""
    total_queries: int
    correct_predictions: int
    accuracy: float
    avg_execution_time: float
    agent_accuracy: Dict[str, float]
    query_type_accuracy: Dict[str, float]
    failed_queries: int
    error_rate: float


class SingleModalEvaluator:
    """单模态查询评估器"""
    
    def __init__(self, benchmark_dir: str = "data/benchmark"):
        """
        初始化评估器
        
        Args:
            benchmark_dir: 基准测试数据目录
        """
        self.benchmark_dir = Path(benchmark_dir)
        
        # 初始化三个智能体
        self.attribute_agent = AttributeFilteringAgent()
        self.structural_agent = StructuralTopologyAgent()
        self.geometry_semantic_agent = GeometrySemanticAgent()
        
        # 智能体映射
        self.agents = {
            "属性查询智能体": self.attribute_agent,
            "结构关系查询智能体": self.structural_agent,
            "几何和语义查询智能体": self.geometry_semantic_agent
        }
        
        self.results: List[SingleModalEvaluationResult] = []
        self.initialized = False
        
    async def initialize(self):
        """初始化所有智能体"""
        if self.initialized:
            return
            
        logger.info("正在初始化单模态评估器...")
        
        # 并行初始化所有智能体
        await asyncio.gather(
            self.attribute_agent.connect(),
            self.structural_agent.connect(),
            self.geometry_semantic_agent.connect()
        )
        
        logger.info("单模态评估器初始化完成")
        self.initialized = True
    
    async def evaluate_all_single_modal_queries(self, 
                                              max_assemblies: Optional[int] = None,
                                              top_k: int = 10) -> SingleModalEvaluationSummary:
        """
        评估所有单模态查询
        
        Args:
            max_assemblies: 最大评估装配体数量（None表示评估所有）
            top_k: 每个查询返回的结果数量
            
        Returns:
            评估总结
        """
        logger.info("开始评估单模态查询...")
        
        # 获取所有装配体目录
        assembly_dirs = [d for d in self.benchmark_dir.iterdir() 
                        if d.is_dir() and d.name != "generation_statistics.json"]
        
        if max_assemblies:
            assembly_dirs = assembly_dirs[:max_assemblies]
            
        logger.info(f"找到 {len(assembly_dirs)} 个装配体，开始评估...")
        
        total_queries = 0
        processed_queries = 0
        
        for assembly_dir in assembly_dirs:
            single_modal_file = assembly_dir / "single_modal_query.json"
            
            if not single_modal_file.exists():
                logger.warning(f"装配体 {assembly_dir.name} 缺少单模态查询文件")
                continue
                
            # 加载单模态查询
            try:
                with open(single_modal_file, 'r', encoding='utf-8') as f:
                    single_modal_queries = json.load(f)
                    
                total_queries += len(single_modal_queries)
                
                # 评估每个单模态查询
                for query in single_modal_queries:
                    try:
                        result = await self._evaluate_single_query(query, top_k)
                        self.results.append(result)
                        processed_queries += 1
                        
                        logger.info(f"查询 {query['query_id']} 评估完成: "
                                  f"{'正确' if result.is_correct else '错误'} "
                                  f"(智能体: {result.agent_name}, 耗时: {result.execution_time:.2f}s)")
                        
                        # 添加延迟避免过于频繁的请求
                        await asyncio.sleep(0.5)
                        
                    except Exception as e:
                        logger.error(f"评估查询 {query.get('query_id', 'unknown')} 失败: {e}")
                        # 记录失败的查询
                        error_result = SingleModalEvaluationResult(
                            query_id=query.get('query_id', 'unknown'),
                            query_type=query.get('query_type', 'unknown'),
                            natural_language_prompt=query.get('natural_language_prompt', ''),
                            ground_truth_uuids=[],
                            predicted_uuids=[],
                            is_correct=False,
                            execution_time=0.0,
                            agent_name='unknown',
                            error_message=str(e)
                        )
                        self.results.append(error_result)
                        
            except Exception as e:
                logger.error(f"加载装配体 {assembly_dir.name} 的单模态查询失败: {e}")
                continue
        
        logger.info(f"评估完成: 总查询数 {total_queries}, 处理查询数 {processed_queries}")
        
        # 生成评估总结
        summary = self._generate_summary()
        return summary

    async def _evaluate_single_query(self, query: Dict[str, Any], top_k: int) -> SingleModalEvaluationResult:
        """
        评估单个查询

        Args:
            query: 查询数据
            top_k: 返回结果数量

        Returns:
            评估结果
        """
        query_id = query['query_id']
        query_type = query['query_type']
        natural_language_prompt = query['natural_language_prompt']

        # 提取ground truth UUIDs
        ground_truth_uuids = []
        for gt_result in query.get('ground_truth_results', []):
            if 'uuid' in gt_result:
                ground_truth_uuids.append(gt_result['uuid'])

        # 根据查询计划确定使用的智能体
        ground_truth_plan = query.get('ground_truth_plan', {})
        sub_queries = ground_truth_plan.get('sub_queries', [])

        if not sub_queries:
            raise ValueError(f"查询 {query_id} 缺少ground_truth_plan中的sub_queries")

        # 获取第一个子查询的智能体名称
        agent_name = sub_queries[0].get('agent', '')

        if agent_name not in self.agents:
            raise ValueError(f"未知的智能体: {agent_name}")

        start_time = time.time()

        try:
            # 根据智能体类型创建相应的任务并执行
            if agent_name == "属性查询智能体":
                task = UnifiedStructuredDataTask(
                    task_id=query_id,
                    query_text=natural_language_prompt,
                    top_k=top_k
                )
                result = await self.attribute_agent.execute_task(task)

            elif agent_name == "结构关系查询智能体":
                task = UnifiedStructuralQueryTask(
                    task_id=query_id,
                    query_text=natural_language_prompt,
                    top_k=top_k
                )
                result = await self.structural_agent.execute_task(task)

            elif agent_name == "几何和语义查询智能体":
                task = UnifiedGeometrySemanticTask(
                    task_id=query_id,
                    query_text=natural_language_prompt,
                    top_k=top_k
                )
                result = await self.geometry_semantic_agent.execute_task(task)

            else:
                raise ValueError(f"不支持的智能体类型: {agent_name}")

            execution_time = time.time() - start_time

            # 提取预测的UUIDs
            predicted_uuids = []
            if result.status == 'success' and result.results:
                predicted_uuids = [item.uuid for item in result.results]

            # 判断是否正确：预测结果中是否包含任何ground truth UUID
            is_correct = any(uuid in predicted_uuids for uuid in ground_truth_uuids)

            return SingleModalEvaluationResult(
                query_id=query_id,
                query_type=query_type,
                natural_language_prompt=natural_language_prompt,
                ground_truth_uuids=ground_truth_uuids,
                predicted_uuids=predicted_uuids,
                is_correct=is_correct,
                execution_time=execution_time,
                agent_name=agent_name,
                error_message=result.error_message if result.status != 'success' else None
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return SingleModalEvaluationResult(
                query_id=query_id,
                query_type=query_type,
                natural_language_prompt=natural_language_prompt,
                ground_truth_uuids=ground_truth_uuids,
                predicted_uuids=[],
                is_correct=False,
                execution_time=execution_time,
                agent_name=agent_name,
                error_message=str(e)
            )

    def _generate_summary(self) -> SingleModalEvaluationSummary:
        """生成评估总结"""
        total_queries = len(self.results)
        correct_predictions = sum(1 for r in self.results if r.is_correct)
        failed_queries = sum(1 for r in self.results if r.error_message is not None)

        accuracy = correct_predictions / total_queries if total_queries > 0 else 0.0
        error_rate = failed_queries / total_queries if total_queries > 0 else 0.0

        # 计算平均执行时间（排除失败的查询）
        successful_times = [r.execution_time for r in self.results if r.error_message is None]
        avg_execution_time = sum(successful_times) / len(successful_times) if successful_times else 0.0

        # 按智能体计算准确率
        agent_accuracy = {}
        agent_names = set(r.agent_name for r in self.results)

        for agent_name in agent_names:
            agent_results = [r for r in self.results if r.agent_name == agent_name]
            agent_correct = sum(1 for r in agent_results if r.is_correct)
            agent_acc = agent_correct / len(agent_results) if agent_results else 0.0
            agent_accuracy[agent_name] = agent_acc

        # 按查询类型计算准确率
        query_type_accuracy = {}
        query_types = set(r.query_type for r in self.results)

        for query_type in query_types:
            type_results = [r for r in self.results if r.query_type == query_type]
            type_correct = sum(1 for r in type_results if r.is_correct)
            type_accuracy = type_correct / len(type_results) if type_results else 0.0
            query_type_accuracy[query_type] = type_accuracy

        return SingleModalEvaluationSummary(
            total_queries=total_queries,
            correct_predictions=correct_predictions,
            accuracy=accuracy,
            avg_execution_time=avg_execution_time,
            agent_accuracy=agent_accuracy,
            query_type_accuracy=query_type_accuracy,
            failed_queries=failed_queries,
            error_rate=error_rate
        )

    def save_detailed_results(self, output_file: str):
        """保存详细的评估结果"""
        results_data = []

        for result in self.results:
            results_data.append({
                'query_id': result.query_id,
                'query_type': result.query_type,
                'natural_language_prompt': result.natural_language_prompt,
                'ground_truth_uuids': result.ground_truth_uuids,
                'predicted_uuids': result.predicted_uuids,
                'is_correct': result.is_correct,
                'execution_time': result.execution_time,
                'agent_name': result.agent_name,
                'error_message': result.error_message
            })

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)

        logger.info(f"详细评估结果已保存到: {output_file}")

    def print_summary(self, summary: SingleModalEvaluationSummary):
        """打印评估总结"""
        print("\n" + "="*60)
        print("单模态查询评估结果总结")
        print("="*60)
        print(f"总查询数: {summary.total_queries}")
        print(f"正确预测数: {summary.correct_predictions}")
        print(f"整体准确率: {summary.accuracy:.2%}")
        print(f"平均执行时间: {summary.avg_execution_time:.2f}秒")
        print(f"失败查询数: {summary.failed_queries}")
        print(f"错误率: {summary.error_rate:.2%}")

        print("\n按智能体的准确率:")
        print("-" * 40)
        for agent_name, accuracy in summary.agent_accuracy.items():
            print(f"{agent_name}: {accuracy:.2%}")

        print("\n按查询类型的准确率:")
        print("-" * 40)
        for query_type, accuracy in summary.query_type_accuracy.items():
            print(f"{query_type}: {accuracy:.2%}")

        print("="*60)


async def main():
    """主函数"""
    evaluator = SingleModalEvaluator()

    try:
        # 初始化评估器
        await evaluator.initialize()

        # 执行评估（可以设置max_assemblies限制评估数量）
        summary = await evaluator.evaluate_all_single_modal_queries(
            max_assemblies=10,  # 先评估10个装配体进行测试
            top_k=10
        )

        # 打印总结
        evaluator.print_summary(summary)

        # 保存详细结果
        output_dir = Path("results/evaluation")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = time.strftime("%Y%m%d_%H%M%S")
        detailed_results_file = output_dir / f"single_modal_evaluation_{timestamp}.json"
        evaluator.save_detailed_results(str(detailed_results_file))

        # 保存总结
        summary_file = output_dir / f"single_modal_summary_{timestamp}.json"
        summary_data = {
            'total_queries': summary.total_queries,
            'correct_predictions': summary.correct_predictions,
            'accuracy': summary.accuracy,
            'avg_execution_time': summary.avg_execution_time,
            'agent_accuracy': summary.agent_accuracy,
            'query_type_accuracy': summary.query_type_accuracy,
            'failed_queries': summary.failed_queries,
            'error_rate': summary.error_rate
        }

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)

        logger.info(f"评估总结已保存到: {summary_file}")

    except Exception as e:
        logger.error(f"评估过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
