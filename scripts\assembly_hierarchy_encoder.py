#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
装配层次结构图编码器

该脚本实现了一种无需训练的图编码方案，将复杂的装配层次图转化为紧凑的向量表示。
主要功能：
1. 从Neo4j数据库获取装配层次图数据
2. 通过递归式自然语言描述生成方法，将图的拓扑结构转化为连贯的自然语言描述
3. 使用BAAI/bge-m3预训练语言模型将文本映射为固定维度特征向量
4. 对文本嵌入进行L2归一化
5. 对所有装配体的嵌入进行PCA降维和L2归一化
6. 保存最终的装配层次结构特征到本地文件

使用方法:
    python scripts/assembly_hierarchy_encoder.py [--pca_components 64] [--batch_size 32]

输出文件:
    data/features/assemblies/assembly_hierarchy_features.pkl
    
输出格式:
    {
        "assembly_id_1": numpy.ndarray,  # 装配体层次结构特征向量
        "assembly_id_2": numpy.ndarray,
        ...
    }
"""

import os
import sys
import pickle
import logging
import argparse
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter

import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import normalize
from tqdm import tqdm

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.knowledge_graph import CADKnowledgeGraph
from src.config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置环境变量，使用国内镜像
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

try:
    from pymilvus.model.hybrid import BGEM3EmbeddingFunction
except ImportError as e:
    logger.error("请安装pymilvus: pip install pymilvus")
    raise


class AssemblyHierarchyEncoder:
    """装配层次结构编码器"""
    
    def __init__(self, device: str = "cuda"):
        """
        初始化编码器
        
        Args:
            device: 计算设备 ('cuda' 或 'cpu')
        """
        self.device = device if device == "cuda" and self._is_cuda_available() else "cpu"
        logger.info(f"使用设备: {self.device}")
        
        # 初始化Neo4j连接
        self.kg = CADKnowledgeGraph.from_config()
        logger.info("Neo4j连接初始化成功")
        
        # 初始化文本嵌入模型
        self._init_embedding_model()
        
    def _is_cuda_available(self) -> bool:
        """检查CUDA是否可用"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def _init_embedding_model(self):
        """初始化BAAI/bge-m3嵌入模型"""
        try:
            # 尝试使用本地缓存的模型路径
            model_path = os.path.expanduser("~/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181")
            if not os.path.exists(model_path):
                # 如果本地缓存不存在，使用模型名称让库自动下载
                model_path = "BAAI/bge-m3"
                logger.info(f"本地模型缓存不存在，将从Hugging Face下载模型: {model_path}")
            
            # 根据设备类型确定是否使用半精度
            use_fp16 = self.device == "cuda"
            self.embedding_function = BGEM3EmbeddingFunction(
                model_name=model_path,
                use_fp16=use_fp16,
                device=self.device
            )
            logger.info("BAAI/bge-m3嵌入模型初始化成功")
            
        except Exception as e:
            logger.error(f"初始化嵌入模型失败: {e}")
            raise
    
    def get_all_assembly_ids(self) -> List[str]:
        """获取数据库中所有装配体的ID"""
        query = """
        MATCH (a:Assembly)
        RETURN a.uuid as assembly_id
        ORDER BY assembly_id
        """
        
        try:
            result = self.kg.run_cypher(query)
            if isinstance(result, list):
                assembly_ids = [record['assembly_id'] for record in result]
                logger.info(f"找到 {len(assembly_ids)} 个装配体")
                return assembly_ids
            else:
                logger.error(f"查询装配体ID失败: {result}")
                return []
        except Exception as e:
            logger.error(f"获取装配体ID失败: {e}")
            return []
    
    def get_assembly_hierarchy_data(self, assembly_id: str) -> Optional[Dict[str, Any]]:
        """
        获取装配体的层次结构数据
        
        Args:
            assembly_id: 装配体ID
            
        Returns:
            装配体层次结构数据字典，包含节点和边信息
        """
        query = f"""
        MATCH path = (a:Assembly {{uuid: '{assembly_id}'}})-[*0..]->(n)
        WHERE n:Assembly OR n:SubAssembly OR n:Part
        WITH collect(DISTINCT n) as all_nodes
        UNWIND all_nodes as node
        OPTIONAL MATCH (node)-[r:hasComponent]->(target)
        WHERE target IN all_nodes
        RETURN 
            node.uuid as node_id,
            labels(node)[0] as node_type,
            node.name as node_name,
            type(r) as rel_type,
            target.uuid as target_id,
            target.name as target_name,
            labels(target)[0] as target_type
        """
        
        try:
            result = self.kg.run_cypher(query)
            if isinstance(result, list):
                return self._process_hierarchy_result(result, assembly_id)
            else:
                logger.error(f"查询装配体 {assembly_id} 层次结构失败: {result}")
                return None
        except Exception as e:
            logger.error(f"获取装配体 {assembly_id} 层次结构数据失败: {e}")
            return None
    
    def _process_hierarchy_result(self, result: List[Dict], assembly_id: str) -> Dict[str, Any]:
        """处理层次结构查询结果"""
        nodes = {}
        edges = []
        
        for record in result:
            node_id = record['node_id']
            node_type = record['node_type']
            node_name = record['node_name'] or f"{node_type}_{node_id[:8]}"
            
            # 收集节点信息
            if node_id not in nodes:
                nodes[node_id] = {
                    'id': node_id,
                    'type': node_type,
                    'name': node_name
                }
            
            # 收集边信息
            if record['rel_type'] == 'hasComponent' and record['target_id']:
                target_id = record['target_id']
                target_name = record['target_name'] or f"{record['target_type']}_{target_id[:8]}"
                
                # 确保目标节点也在节点字典中
                if target_id not in nodes:
                    nodes[target_id] = {
                        'id': target_id,
                        'type': record['target_type'],
                        'name': target_name
                    }
                
                edges.append({
                    'source': node_id,
                    'target': target_id,
                    'type': 'hasComponent'
                })
        
        return {
            'assembly_id': assembly_id,
            'nodes': nodes,
            'edges': edges
        }
    
    def generate_hierarchy_description(self, hierarchy_data: Dict[str, Any]) -> str:
        """
        生成装配层次结构的JSON格式描述

        Args:
            hierarchy_data: 装配层次结构数据

        Returns:
            装配层次结构的JSON格式描述字符串
        """
        if not hierarchy_data or not hierarchy_data['nodes']:
            return '{"Assembly": {"Name": "Empty", "PathEncoding": "Root", "Depth": 0, "Parts": []}}'

        nodes = hierarchy_data['nodes']
        edges = hierarchy_data['edges']
        assembly_id = hierarchy_data['assembly_id']

        # 构建父子关系映射
        children_map = defaultdict(list)
        for edge in edges:
            children_map[edge['source']].append(edge['target'])

        # 找到根节点（装配体）
        root_node = None
        for node in nodes.values():
            if node['type'] == 'Assembly':
                root_node = node
                break

        if not root_node:
            return '{"Assembly": {"Name": "NotFound", "PathEncoding": "Root", "Depth": 0, "Parts": []}}'

        # 递归生成JSON结构
        json_structure = self._generate_json_structure(root_node, nodes, children_map, f"/{root_node['name']}", 0)

        # 转换为JSON字符串
        import json
        return json.dumps(json_structure, ensure_ascii=False)
    
    def _generate_json_structure(self, node: Dict, nodes: Dict, children_map: Dict, path: str, depth: int) -> Dict:
        """
        递归生成节点的JSON结构

        Args:
            node: 当前节点
            nodes: 所有节点字典
            children_map: 父子关系映射
            path: 路径
            depth: 当前深度

        Returns:
            节点及其子节点的JSON结构
        """
        node_id = node['id']
        node_type = node['type']
        node_name = node['name']

        # 获取子节点
        child_ids = children_map.get(node_id, [])
        children = [nodes[child_id] for child_id in child_ids if child_id in nodes]

        # 按类型分组子节点
        parts = [child for child in children if child['type'] == 'Part']
        subassemblies = [child for child in children if child['type'] == 'SubAssembly']

        # 构建当前节点的基本JSON结构
        json_node = {
            "name": node_name,
            "type": node_type,
            "depth": depth,
            "path": path,
            "children": []
        }

        # 统计相同名称的组件数量
        from collections import Counter
        part_counts = Counter(part['name'] for part in parts)
        subassembly_counts = Counter(sub['name'] for sub in subassemblies)

        # 处理零件 - 按名称分组并添加数量信息
        processed_parts = set()
        for part in parts:
            part_name = part['name']
            if part_name in processed_parts:
                continue

            part_path = f"{path}/{part_name}"
            part_json = {
                "name": part_name,
                "type": "Part",
                "depth": depth + 1,
                "path": part_path,
                "children": []
            }

            # 如果有多个相同名称的零件，添加数量信息
            count = part_counts[part_name]
            if count > 1:
                part_json["quantity"] = count

            json_node["children"].append(part_json)
            processed_parts.add(part_name)

        # 处理子装配体 - 按名称分组并添加数量信息
        processed_subassemblies = set()
        for subassembly in subassemblies:
            sub_name = subassembly['name']
            if sub_name in processed_subassemblies:
                continue

            sub_path = f"{path}/{sub_name}"
            sub_json = self._generate_json_structure(subassembly, nodes, children_map, sub_path, depth + 1)

            # 如果有多个相同名称的子装配体，添加数量信息
            count = subassembly_counts[sub_name]
            if count > 1:
                sub_json["quantity"] = count

            json_node["children"].append(sub_json)
            processed_subassemblies.add(sub_name)

        return json_node
    
    def encode_text_to_embedding(self, text: str) -> np.ndarray:
        """
        将文本编码为嵌入向量并进行L2归一化
        
        Args:
            text: 输入文本
            
        Returns:
            L2归一化后的嵌入向量
        """
        try:
            # 使用BAAI/bge-m3生成嵌入
            embeddings = self.embedding_function([text])
            
            # 获取密集向量
            dense_embedding = embeddings["dense"][0]
            
            # 转换为numpy数组
            embedding_array = np.array(dense_embedding)
            
            # L2归一化
            normalized_embedding = normalize(embedding_array.reshape(1, -1), norm='l2')[0]
            
            return normalized_embedding
            
        except Exception as e:
            logger.error(f"文本嵌入编码失败: {e}")
            # 返回零向量作为回退
            return np.zeros(1024)  # BAAI/bge-m3的默认维度
    
    def encode_all_assemblies(self, batch_size: int = 32) -> Dict[str, np.ndarray]:
        """
        编码所有装配体的层次结构
        
        Args:
            batch_size: 批处理大小
            
        Returns:
            装配体ID到嵌入向量的映射
        """
        assembly_ids = self.get_all_assembly_ids()
        if not assembly_ids:
            logger.error("未找到任何装配体")
            return {}
        
        assembly_embeddings = {}
        failed_assemblies = []
        
        logger.info(f"开始编码 {len(assembly_ids)} 个装配体的层次结构...")
        
        for assembly_id in tqdm(assembly_ids, desc="编码装配体层次结构"):
            try:
                # 获取层次结构数据
                hierarchy_data = self.get_assembly_hierarchy_data(assembly_id)
                if not hierarchy_data:
                    logger.warning(f"装配体 {assembly_id} 层次结构数据为空，跳过")
                    failed_assemblies.append(assembly_id)
                    continue
                
                # 生成自然语言描述
                description = self.generate_hierarchy_description(hierarchy_data)
                if not description or description.strip() == "":
                    logger.warning(f"装配体 {assembly_id} 描述生成失败，跳过")
                    failed_assemblies.append(assembly_id)
                    continue
                
                # 编码为嵌入向量
                embedding = self.encode_text_to_embedding(description)
                assembly_embeddings[assembly_id] = embedding
                
                # 记录一些示例描述
                if len(assembly_embeddings) <= 3:
                    logger.info(f"装配体 {assembly_id} 描述示例: {description[:200]}...")
                
            except Exception as e:
                logger.error(f"编码装配体 {assembly_id} 失败: {e}")
                failed_assemblies.append(assembly_id)
                continue
        
        logger.info(f"成功编码 {len(assembly_embeddings)} 个装配体，失败 {len(failed_assemblies)} 个")
        if failed_assemblies:
            logger.warning(f"失败的装配体ID: {failed_assemblies[:10]}...")
        
        return assembly_embeddings
    
    def apply_pca_and_normalize(self, embeddings: Dict[str, np.ndarray], 
                               n_components: int = 64) -> Dict[str, np.ndarray]:
        """
        对嵌入向量进行PCA降维和L2归一化
        
        Args:
            embeddings: 装配体嵌入向量字典
            n_components: PCA降维后的维度
            
        Returns:
            处理后的嵌入向量字典
        """
        if not embeddings:
            logger.error("嵌入向量字典为空")
            return {}
        
        assembly_ids = list(embeddings.keys())
        embedding_matrix = np.array(list(embeddings.values()))
        
        logger.info(f"原始嵌入维度: {embedding_matrix.shape}")
        
        # 确保降维后的维度不超过原始维度和样本数量
        n_components = min(n_components, embedding_matrix.shape[1], embedding_matrix.shape[0])
        
        # 应用PCA降维
        pca = PCA(n_components=n_components, random_state=42)
        reduced_embeddings = pca.fit_transform(embedding_matrix)
        
        logger.info(f"PCA降维后维度: {reduced_embeddings.shape}")
        logger.info(f"保留方差比例: {pca.explained_variance_ratio_.sum():.4f}")
        
        # L2归一化
        normalized_embeddings = normalize(reduced_embeddings, norm='l2')
        
        # 转换回字典格式
        processed_embeddings = {}
        for i, assembly_id in enumerate(assembly_ids):
            processed_embeddings[assembly_id] = normalized_embeddings[i]
        
        logger.info("PCA降维和L2归一化完成")
        return processed_embeddings
    
    def save_features(self, embeddings: Dict[str, np.ndarray], output_path: str):
        """
        保存装配层次结构特征到文件
        
        Args:
            embeddings: 装配体嵌入向量字典
            output_path: 输出文件路径
        """
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 保存到pickle文件
        with open(output_path, 'wb') as f:
            pickle.dump(embeddings, f)
        
        logger.info(f"装配层次结构特征已保存到: {output_path}")
        logger.info(f"包含 {len(embeddings)} 个装配体的特征向量")
        
        # 输出一些统计信息
        if embeddings:
            sample_embedding = next(iter(embeddings.values()))
            logger.info(f"特征向量维度: {sample_embedding.shape[0]}")
            
            all_embeddings = np.array(list(embeddings.values()))
            logger.info(f"特征向量范围: [{all_embeddings.min():.4f}, {all_embeddings.max():.4f}]")
            logger.info(f"特征向量均值: {all_embeddings.mean():.4f}")
            logger.info(f"特征向量标准差: {all_embeddings.std():.4f}")
    
    def close(self):
        """关闭数据库连接"""
        if hasattr(self, 'kg') and self.kg:
            self.kg.close()
            logger.info("Neo4j连接已关闭")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="装配层次结构图编码器")
    parser.add_argument("--pca_components", type=int, default=64,
                       help="PCA降维后的维度 (默认: 64)")
    parser.add_argument("--batch_size", type=int, default=32,
                       help="批处理大小 (默认: 32)")
    parser.add_argument("--device", choices=["cuda", "cpu"], default="cuda",
                       help="计算设备 (默认: cuda)")
    parser.add_argument("--output", default="data/features/assemblies/assembly_hierarchy_features.pkl",
                       help="输出文件路径")
    
    args = parser.parse_args()
    
    # 创建编码器
    encoder = AssemblyHierarchyEncoder(device=args.device)
    
    try:
        # 编码所有装配体
        logger.info("开始编码装配体层次结构...")
        embeddings = encoder.encode_all_assemblies(batch_size=args.batch_size)
        
        if not embeddings:
            logger.error("未能编码任何装配体，程序退出")
            return
        
        # PCA降维和归一化
        logger.info("开始PCA降维和L2归一化...")
        processed_embeddings = encoder.apply_pca_and_normalize(
            embeddings, n_components=args.pca_components
        )
        
        # 保存特征
        logger.info("保存装配层次结构特征...")
        encoder.save_features(processed_embeddings, args.output)
        
        logger.info("装配层次结构编码完成！")
        
    except Exception as e:
        logger.error(f"编码过程中发生错误: {e}")
        raise
    finally:
        # 关闭连接
        encoder.close()


if __name__ == "__main__":
    main()
