#!/usr/bin/env python3
"""
运行单模态查询评估的脚本
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from single_modal_evaluator import SingleModalEvaluator


async def run_evaluation(benchmark_dir: str, max_assemblies: int = None, top_k: int = 10):
    """
    运行单模态查询评估
    
    Args:
        benchmark_dir: 基准测试数据目录
        max_assemblies: 最大评估装配体数量
        top_k: 每个查询返回的结果数量
    """
    evaluator = SingleModalEvaluator(benchmark_dir=benchmark_dir)
    
    try:
        # 初始化评估器
        print("🚀 正在初始化单模态评估器...")
        await evaluator.initialize()
        
        # 执行评估
        print(f"📊 开始评估单模态查询 (max_assemblies={max_assemblies}, top_k={top_k})...")
        summary = await evaluator.evaluate_all_single_modal_queries(
            max_assemblies=max_assemblies,
            top_k=top_k
        )
        
        # 打印总结
        evaluator.print_summary(summary)
        
        # 保存结果
        output_dir = Path("results/evaluation")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        import time
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        detailed_results_file = output_dir / f"single_modal_evaluation_{timestamp}.json"
        evaluator.save_detailed_results(str(detailed_results_file))
        
        # 保存总结
        summary_file = output_dir / f"single_modal_summary_{timestamp}.json"
        summary_data = {
            'total_queries': summary.total_queries,
            'correct_predictions': summary.correct_predictions,
            'accuracy': summary.accuracy,
            'avg_execution_time': summary.avg_execution_time,
            'agent_accuracy': summary.agent_accuracy,
            'query_type_accuracy': summary.query_type_accuracy,
            'failed_queries': summary.failed_queries,
            'error_rate': summary.error_rate
        }
        
        import json
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 评估完成！")
        print(f"📄 详细结果: {detailed_results_file}")
        print(f"📊 评估总结: {summary_file}")
        
        return summary
        
    except Exception as e:
        print(f"❌ 评估过程中发生错误: {e}")
        raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行单模态查询评估")
    
    parser.add_argument(
        "--benchmark_dir", 
        type=str, 
        default="data/benchmark",
        help="基准测试数据目录 (默认: data/benchmark)"
    )
    
    parser.add_argument(
        "--max_assemblies", 
        type=int, 
        default=None,
        help="最大评估装配体数量 (默认: 评估所有装配体)"
    )
    
    parser.add_argument(
        "--top_k", 
        type=int, 
        default=100,
        help="每个查询返回的结果数量 (默认: 10)"
    )
    
    args = parser.parse_args()
    
    # 检查基准测试目录是否存在
    if not Path(args.benchmark_dir).exists():
        print(f"❌ 基准测试目录不存在: {args.benchmark_dir}")
        sys.exit(1)
    
    # 运行评估
    asyncio.run(run_evaluation(
        benchmark_dir=args.benchmark_dir,
        max_assemblies=args.max_assemblies,
        top_k=args.top_k
    ))


if __name__ == "__main__":
    main()
